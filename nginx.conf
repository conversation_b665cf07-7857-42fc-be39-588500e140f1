upstream web {
    server web:8000;
}
upstream socket {
    server web:8001;
}

server {

    # HTTP/3 settings

    # HTTP/3 and Quic Listen
    listen 443 quic reuseport;
    http3 on;

    # HTTP/3 Add Alt-Svc header negotiation.
    add_header alt-svc 'h2=":443"; ma=86400, h3-29=":443"; ma=86400, h3=":443"; ma=86400' always;

    # Enable TLSv1.3's 0-RTT. Use $ssl_early_data when reverse proxying to
    # prevent replay attacks.
    # @see: http://nginx.org/en/docs/http/ngx_http_ssl_module.html#ssl_early_data
    ssl_early_data on;
    quic_retry on;
    quic_gso on;
    quic_host_key /etc/letsencrypt/live/${WHISKERS_HUB_DOMAIN}/privkey.pem;
    # /End of HTTP/3.
    
    # HTTP/2 Listen
    listen 80 http2;
    listen 443 ssl http2;
    http2 on;
    server_name ${SERVER_NAME};
    
    ssl_certificate     /etc/letsencrypt/live/${WHISKERS_HUB_DOMAIN}/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/${WHISKERS_HUB_DOMAIN}/privkey.pem;

     # HTTP/2 settings
    http2_push_preload on;
    # /End of HTTP/2.

    location /ws/ {
        proxy_pass http://socket;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_redirect off;
    }
    location /routes/ttn-app/ {
        proxy_pass http://socket;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_redirect off;
    }

    location /static/ {
        alias /home/<USER>/staticfiles/;
    }

    location /media/ {
        alias /home/<USER>/media/;
    }
    
    location / {
        proxy_pass http://web;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $host;
        proxy_redirect off;
        client_max_body_size 0;
        proxy_read_timeout 1800;
        proxy_ignore_client_abort on;
    }
}