#!/bin/bash

# Ensure directories exist with correct permissions
echo "Setting up directories..."
mkdir -p /home/<USER>/staticfiles /home/<USER>/media

# Run migrations
echo "Running migrations..."
python manage.py migrate

# Collect static files
echo "Collecting static files..."
python manage.py collectstatic --noinput

# Run last update check
echo "Running last update check..."
python scripts/last_update_check.py &

# Execute the command passed to docker
echo "Starting application..."
exec "$@"