version: "3.8"

services:
  redis:
    image: redis:7.4.2
    command: redis-server /usr/local/etc/redis/redis.conf
    restart: unless-stopped
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "-p", "6380", "ping"]
      interval: 10s
      retries: 100
      timeout: 5s
    volumes:
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    env_file:
      - ./.env

  web:
    build:
      context: .
      dockerfile: ${DOCKER_FILE}
    command: >
      sh -c "
      gunicorn app.wsgi:application --bind 0.0.0.0:8000 --workers ${GUNICORN_WORKERS} &
      uvicorn app.asgi:application --host 0.0.0.0 --port 8001 --workers ${UVICORN_WORKERS}
      "
    restart: unless-stopped
    volumes:
      - .:/home/<USER>
    env_file:
      - ./.env
    ports:
      - 8000:8000
      - 8001:8001
    depends_on:
      pgbouncer:
        condition: service_healthy
      redis:
        condition: service_healthy

  db:
    image: postgres:13.0-alpine
    restart: unless-stopped
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U postgres" ]
      interval: 10s
      timeout: 3s
      retries: 100
    volumes:
      - db:/var/lib/postgresql/data/
    env_file:
      - ./.env


  pgbouncer:
    image: edoburu/pgbouncer:v1.24.0-p1
    restart: unless-stopped
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U postgres -h db" ]
      interval: 10s
      timeout: 3s
      retries: 100
    env_file:
      - ./.env
    depends_on:
      - db

  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile 
    restart: unless-stopped
    volumes:
      - ./staticfiles:/home/<USER>/staticfiles
      - ./media:/home/<USER>/media
      - ./nginx.conf:/etc/nginx/user_conf.d/nginx.conf:ro
      - ./nginx-local:/etc/letsencrypt/live/localhost
      - letsencrypt:/etc/letsencrypt
    env_file:
      - ./.env
    environment:
      ENVSUBST_VARS: WHISKERS_HUB_DOMAIN,SERVER_NAME
    ports:
      - 80:80
      - 443:443
    depends_on:
      - web
  
volumes:
  db:
  letsencrypt:
  pgdata:
